<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gifted Brain Schools - Summer School Fun Splash</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400&family=Bubblegum+Sans:wght@400&family=Open+Sans:wght@400;600;700;800&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .flyer {
            width: 8.5in;
            height: 11in;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 30%, #FF6347 70%, #DC143C 100%);
            position: relative;
            font-family: 'Open Sans', sans-serif;
            overflow: hidden;
            margin: 0 auto;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(90deg, #DC143C, #FF4500);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
            border-bottom: 5px solid #FFD700;
        }

        .school-name {
            font-family: 'Fredoka One', cursive;
            font-size: 28px;
            font-weight: 800;
            letter-spacing: 2px;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .photo-strip {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            padding: 0 20px;
        }
        
        .photo {
            width: 120px;
            height: 90px;
            background: white;
            border: 3px solid white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            transform: rotate(-2deg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        
        .photo:nth-child(2) {
            transform: rotate(1deg);
        }
        
        .photo:nth-child(3) {
            transform: rotate(-1deg);
        }
        
        .main-title {
            text-align: center;
            margin: 20px 0;
        }
        
        .champions {
            font-family: 'Fredoka One', cursive;
            font-size: 36px;
            color: #4169E1;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 5px;
        }
        
        .summer-school {
            font-family: 'Fredoka One', cursive;
            font-size: 48px;
            background: linear-gradient(45deg, #FF6B35, #F7931E, #FFD700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            line-height: 1;
        }
        
        .age-tags {
            display: flex;
            justify-content: space-between;
            margin: 20px 40px;
        }
        
        .age-tag {
            background: #FF69B4;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 700;
            font-size: 14px;
            text-transform: uppercase;
        }
        
        .age-tag.left {
            background: #32CD32;
        }
        
        .age-tag.right {
            background: #FF4500;
        }
        
        .content-section {
            display: flex;
            justify-content: space-between;
            margin: 20px 30px;
            gap: 20px;
        }
        
        .info-box {
            flex: 1;
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .info-header {
            background: #4169E1;
            color: white;
            text-align: center;
            padding: 8px;
            border-radius: 8px;
            font-weight: 700;
            font-size: 14px;
            margin-bottom: 10px;
            text-transform: uppercase;
        }
        
        .info-header.skills {
            background: #32CD32;
        }
        
        .info-header.duration {
            background: #FF4500;
        }
        
        .info-header.academics {
            background: #9932CC;
        }
        
        .info-list {
            font-size: 12px;
            line-height: 1.4;
        }
        
        .info-list li {
            margin-bottom: 3px;
        }
        
        .duration-box {
            text-align: center;
        }
        
        .weeks {
            font-size: 48px;
            font-weight: 800;
            color: #4169E1;
            line-height: 1;
        }
        
        .weeks-text {
            font-size: 16px;
            font-weight: 700;
            color: #333;
        }
        
        .dates {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        
        .footer {
            background: #1E90FF;
            color: white;
            padding: 15px 30px;
            margin-top: 20px;
            text-align: center;
        }
        
        .contact-info {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .phone-numbers {
            font-size: 18px;
            font-weight: 800;
            letter-spacing: 1px;
        }
        
        .decorative-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .star {
            position: absolute;
            color: #FFD700;
            font-size: 20px;
            animation: twinkle 2s infinite;
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
        
        .star:nth-child(1) { top: 15%; left: 10%; animation-delay: 0s; }
        .star:nth-child(2) { top: 25%; right: 15%; animation-delay: 0.5s; }
        .star:nth-child(3) { top: 45%; left: 5%; animation-delay: 1s; }
        .star:nth-child(4) { top: 65%; right: 10%; animation-delay: 1.5s; }
    </style>
</head>
<body>
    <div class="flyer">
        <div class="decorative-elements">
            <div class="star">★</div>
            <div class="star">★</div>
            <div class="star">★</div>
            <div class="star">★</div>
        </div>
        
        <div class="header">
            <div class="school-name">EXCELLENCE ACADEMY</div>
        </div>
        
        <div class="photo-strip">
            <div class="photo">Students Learning<br>Together</div>
            <div class="photo">Science<br>Experiments</div>
            <div class="photo">Creative<br>Activities</div>
        </div>
        
        <div class="main-title">
            <div class="champions">CHAMPIONS</div>
            <div class="summer-school">SUMMER<br>SCHOOL</div>
        </div>
        
        <div class="age-tags">
            <div class="age-tag left">ELEMENTARY<br>GRADES 1-5</div>
            <div class="age-tag">MIDDLE SCHOOL<br>GRADES 6-8</div>
            <div class="age-tag right">HIGH SCHOOL<br>GRADES 9-12</div>
        </div>
        
        <div class="content-section">
            <div class="info-box">
                <div class="info-header skills">ACTIVITIES</div>
                <ul class="info-list">
                    <li>• STEM & ROBOTICS</li>
                    <li>• CREATIVE WRITING</li>
                    <li>• ART & CRAFTS</li>
                    <li>• SPORTS & FITNESS</li>
                    <li>• MUSIC & DRAMA</li>
                    <li>• CODING & TECH</li>
                    <li>• FIELD TRIPS</li>
                </ul>
            </div>
            
            <div class="info-box">
                <div class="info-header duration">DURATION</div>
                <div class="duration-box">
                    <div class="weeks">6</div>
                    <div class="weeks-text">WEEKS</div>
                    <div class="dates">JUNE 15TH -<br>JULY 26TH<br>2024</div>
                </div>
            </div>
            
            <div class="info-box">
                <div class="info-header academics">ACADEMICS</div>
                <ul class="info-list">
                    <li>• READING COMPREHENSION</li>
                    <li>• MATHEMATICS</li>
                    <li>• SCIENCE EXPERIMENTS</li>
                    <li>• CREATIVE WRITING</li>
                    <li>• CRITICAL THINKING</li>
                    <li>• STUDY SKILLS</li>
                    <li>• TEST PREP</li>
                </ul>
            </div>
        </div>
        
        <div class="footer">
            <div class="contact-info">
                REGISTER NOW! • EARLY BIRD DISCOUNT: $299/WEEK • FULL PROGRAM: $1,599<br>
                LOCATION: 123 EDUCATION BLVD, LEARNING CITY • WWW.EXCELLENCEACADEMY.EDU
            </div>
            <div class="phone-numbers">📞 (************* • (*************</div>
        </div>
    </div>
</body>
</html>
