var _JUPYTERLAB;(()=>{"use strict";var e,r,t,a,l,o,n,u,i,d,f,s,c,p,b,h,m,y,v,g={3846:(e,r,t)=>{var a={"./index":()=>Promise.all([t.e(101),t.e(29),t.e(308),t.e(47),t.e(211),t.e(661)]).then((()=>()=>t(4661))),"./extension":()=>Promise.all([t.e(101),t.e(29),t.e(308),t.e(47),t.e(211),t.e(661)]).then((()=>()=>t(4661)))},l=(e,r)=>(t.R=r,r=t.o(a,e)?a[e]():Promise.resolve().then((()=>{throw new Error('Module "'+e+'" does not exist in container.')})),t.R=void 0,r),o=(e,r)=>{if(t.S){var a="default",l=t.S[a];if(l&&l!==e)throw new Error("Container initialization failed as it has already been initialized with a different share scope");return t.S[a]=e,t.I(a,r)}};t.d(r,{get:()=>l,init:()=>o})}},j={};function w(e){var r=j[e];if(void 0!==r)return r.exports;var t=j[e]={id:e,loaded:!1,exports:{}};return g[e].call(t.exports,t,t.exports,w),t.loaded=!0,t.exports}w.m=g,w.c=j,w.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return w.d(r,{a:r}),r},w.d=(e,r)=>{for(var t in r)w.o(r,t)&&!w.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},w.f={},w.e=e=>Promise.all(Object.keys(w.f).reduce(((r,t)=>(w.f[t](e,r),r)),[])),w.u=e=>e+"."+{29:"5b560f1c12ce3b87cf0c",47:"6e3bcb22128b9a25cec9",75:"4d46c187e341a0c63970",101:"f89644b3398d2e70c43d",110:"80ba3f0dbf6a5e5a84ab",193:"eec6d29e718d1ba9c3cc",211:"419d5d0f2005e17199d1",308:"e3a0af53917ab788f5da",586:"cd8ca8609577a1026d9e",604:"38afdfa6acb6e1f9e00b",630:"0ebffce3ebab30bbfd1b",635:"441122f24ef474da0b80",661:"bd239b6ff6d2fd9fcb36",704:"3b460f2f9b3791ee46a1",753:"dc5196461f2b1907272a",795:"3276f9de30410a267bce",909:"c58d19560d36a44c4493",952:"e33cd11fa7804ddfa2b7"}[e]+".js?v="+{29:"5b560f1c12ce3b87cf0c",47:"6e3bcb22128b9a25cec9",75:"4d46c187e341a0c63970",101:"f89644b3398d2e70c43d",110:"80ba3f0dbf6a5e5a84ab",193:"eec6d29e718d1ba9c3cc",211:"419d5d0f2005e17199d1",308:"e3a0af53917ab788f5da",586:"cd8ca8609577a1026d9e",604:"38afdfa6acb6e1f9e00b",630:"0ebffce3ebab30bbfd1b",635:"441122f24ef474da0b80",661:"bd239b6ff6d2fd9fcb36",704:"3b460f2f9b3791ee46a1",753:"dc5196461f2b1907272a",795:"3276f9de30410a267bce",909:"c58d19560d36a44c4493",952:"e33cd11fa7804ddfa2b7"}[e],w.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),w.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),w.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),e={},r="@jupyter-lsp/jupyterlab-lsp:",w.l=(t,a,l,o)=>{if(e[t])e[t].push(a);else{var n,u;if(void 0!==l)for(var i=document.getElementsByTagName("script"),d=0;d<i.length;d++){var f=i[d];if(f.getAttribute("src")==t||f.getAttribute("data-webpack")==r+l){n=f;break}}n||(u=!0,(n=document.createElement("script")).charset="utf-8",n.timeout=120,w.nc&&n.setAttribute("nonce",w.nc),n.setAttribute("data-webpack",r+l),n.src=t),e[t]=[a];var s=(r,a)=>{n.onerror=n.onload=null,clearTimeout(c);var l=e[t];if(delete e[t],n.parentNode&&n.parentNode.removeChild(n),l&&l.forEach((e=>e(a))),r)return r(a)},c=setTimeout(s.bind(null,void 0,{type:"timeout",target:n}),12e4);n.onerror=s.bind(null,n.onerror),n.onload=s.bind(null,n.onload),u&&document.head.appendChild(n)}},w.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},w.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{w.S={};var e={},r={};w.I=(t,a)=>{a||(a=[]);var l=r[t];if(l||(l=r[t]={}),!(a.indexOf(l)>=0)){if(a.push(l),e[t])return e[t];w.o(w.S,t)||(w.S[t]={});var o=w.S[t],n="@jupyter-lsp/jupyterlab-lsp",u=(e,r,t,a)=>{var l=o[e]=o[e]||{},u=l[r];(!u||!u.loaded&&(!a!=!u.eager?a:n>u.from))&&(l[r]={get:t,from:n,eager:!!a})},i=[];return"default"===t&&(u("@jupyter-lsp/code-jumpers","2.0.0",(()=>Promise.all([w.e(308),w.e(211),w.e(110)]).then((()=>()=>w(9110))))),u("@jupyter-lsp/completion-theme","4.0.1",(()=>Promise.all([w.e(29),w.e(308),w.e(47),w.e(704)]).then((()=>()=>w(1704))))),u("@jupyter-lsp/jupyterlab-lsp","5.0.1",(()=>Promise.all([w.e(101),w.e(29),w.e(308),w.e(47),w.e(211),w.e(661)]).then((()=>()=>w(4661))))),u("@jupyter-lsp/theme-material","3.0.0",(()=>Promise.all([w.e(193),w.e(630)]).then((()=>()=>w(2630))))),u("@jupyter-lsp/theme-vscode","3.0.0",(()=>Promise.all([w.e(193),w.e(604),w.e(795)]).then((()=>()=>w(5604))))),u("@rjsf/validator-ajv8","5.12.1",(()=>Promise.all([w.e(635),w.e(29)]).then((()=>()=>w(4635))))),u("lodash.mergewith","4.6.2",(()=>w.e(75).then((()=>()=>w(7075)))))),e[t]=i.length?Promise.all(i).then((()=>e[t]=1)):1}}})(),(()=>{var e;w.g.importScripts&&(e=w.g.location+"");var r=w.g.document;if(!e&&r&&(r.currentScript&&(e=r.currentScript.src),!e)){var t=r.getElementsByTagName("script");if(t.length)for(var a=t.length-1;a>-1&&!e;)e=t[a--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),w.p=e})(),t=e=>{var r=e=>e.split(".").map((e=>+e==e?+e:e)),t=/^([^-+]+)?(?:-([^+]+))?(?:\+(.+))?$/.exec(e),a=t[1]?r(t[1]):[];return t[2]&&(a.length++,a.push.apply(a,r(t[2]))),t[3]&&(a.push([]),a.push.apply(a,r(t[3]))),a},a=(e,r)=>{e=t(e),r=t(r);for(var a=0;;){if(a>=e.length)return a<r.length&&"u"!=(typeof r[a])[0];var l=e[a],o=(typeof l)[0];if(a>=r.length)return"u"==o;var n=r[a],u=(typeof n)[0];if(o!=u)return"o"==o&&"n"==u||"s"==u||"u"==o;if("o"!=o&&"u"!=o&&l!=n)return l<n;a++}},l=e=>{var r=e[0],t="";if(1===e.length)return"*";if(r+.5){t+=0==r?">=":-1==r?"<":1==r?"^":2==r?"~":r>0?"=":"!=";for(var a=1,o=1;o<e.length;o++)a--,t+="u"==(typeof(u=e[o]))[0]?"-":(a>0?".":"")+(a=2,u);return t}var n=[];for(o=1;o<e.length;o++){var u=e[o];n.push(0===u?"not("+i()+")":1===u?"("+i()+" || "+i()+")":2===u?n.pop()+" "+n.pop():l(u))}return i();function i(){return n.pop().replace(/^\((.+)\)$/,"$1")}},o=(e,r)=>{if(0 in e){r=t(r);var a=e[0],l=a<0;l&&(a=-a-1);for(var n=0,u=1,i=!0;;u++,n++){var d,f,s=u<e.length?(typeof e[u])[0]:"";if(n>=r.length||"o"==(f=(typeof(d=r[n]))[0]))return!i||("u"==s?u>a&&!l:""==s!=l);if("u"==f){if(!i||"u"!=s)return!1}else if(i)if(s==f)if(u<=a){if(d!=e[u])return!1}else{if(l?d>e[u]:d<e[u])return!1;d!=e[u]&&(i=!1)}else if("s"!=s&&"n"!=s){if(l||u<=a)return!1;i=!1,u--}else{if(u<=a||f<s!=l)return!1;i=!1}else"s"!=s&&"n"!=s&&(i=!1,u--)}}var c=[],p=c.pop.bind(c);for(n=1;n<e.length;n++){var b=e[n];c.push(1==b?p()|p():2==b?p()&p():b?o(b,r):!p())}return!!p()},n=(e,r)=>{var t=w.S[e];if(!t||!w.o(t,r))throw new Error("Shared module "+r+" doesn't exist in shared scope "+e);return t},u=(e,r)=>{var t=e[r];return Object.keys(t).reduce(((e,r)=>!e||!t[e].loaded&&a(e,r)?r:e),0)},i=(e,r,t,a)=>"Unsatisfied version "+t+" from "+(t&&e[r][t].from)+" of shared singleton module "+r+" (required "+l(a)+")",d=(e,r,t,a)=>{var l=u(e,t);return o(a,l)||s(i(e,t,l,a)),c(e[t][l])},f=(e,r,t)=>{var l=e[r];return(r=Object.keys(l).reduce(((e,r)=>!o(t,r)||e&&!a(e,r)?e:r),0))&&l[r]},s=e=>{"undefined"!=typeof console&&console.warn&&console.warn(e)},c=e=>(e.loaded=1,e.get()),b=(p=e=>function(r,t,a,l){var o=w.I(r);return o&&o.then?o.then(e.bind(e,r,w.S[r],t,a,l)):e(r,w.S[r],t,a,l)})(((e,r,t,a)=>(n(e,t),d(r,0,t,a)))),h=p(((e,r,t,a,l)=>{var o=r&&w.o(r,t)&&f(r,t,a);return o?c(o):l()})),m={},y={6029:()=>b("default","react",[1,18,2,0]),7308:()=>b("default","@jupyterlab/apputils",[1,4,1,9]),4085:()=>b("default","@jupyterlab/ui-components",[1,4,0,9]),7930:()=>b("default","@lumino/coreutils",[1,2,0,0]),9053:()=>b("default","@jupyterlab/translation",[1,4,0,9]),6211:()=>b("default","@codemirror/view",[1,6,9,6]),143:()=>b("default","@jupyterlab/logconsole",[1,4,0,9]),667:()=>b("default","@jupyterlab/statusbar",[1,4,0,9]),828:()=>b("default","@jupyterlab/lsp",[1,4,0,9]),1165:()=>b("default","@jupyterlab/docmanager",[1,4,0,9]),1589:()=>h("default","@jupyter-lsp/theme-vscode",[2,3,0,0],(()=>Promise.all([w.e(193),w.e(604)]).then((()=>()=>w(5604))))),2697:()=>h("default","@jupyter-lsp/code-jumpers",[2,2,0,0],(()=>w.e(909).then((()=>()=>w(9110))))),2964:()=>b("default","@jupyterlab/completer",[1,4,0,9]),3205:()=>b("default","@jupyterlab/application",[1,4,0,9]),3849:()=>h("default","@rjsf/validator-ajv8",[1,5,12,1],(()=>w.e(635).then((()=>()=>w(4635))))),4034:()=>h("default","@jupyter-lsp/completion-theme",[2,4,0,1],(()=>w.e(586).then((()=>()=>w(1704))))),4901:()=>b("default","@lumino/signaling",[1,2,0,0]),6264:()=>b("default","@jupyterlab/tooltip",[1,4,0,9]),6697:()=>b("default","@lumino/algorithm",[1,2,0,0]),6797:()=>b("default","@lumino/polling",[1,2,0,0]),7138:()=>b("default","@lezer/highlight",[1,1,0,0]),7454:()=>b("default","@jupyterlab/codemirror",[1,4,0,9]),7749:()=>b("default","@jupyterlab/coreutils",[1,6,0,9]),7807:()=>b("default","@jupyterlab/codeeditor",[1,4,0,9]),8190:()=>b("default","@jupyterlab/settingregistry",[1,4,0,9]),8204:()=>b("default","@codemirror/state",[1,6,2,0]),8429:()=>h("default","@jupyter-lsp/theme-material",[2,3,0,0],(()=>Promise.all([w.e(193),w.e(952)]).then((()=>()=>w(2630))))),8644:()=>h("default","lodash.mergewith",[1,4,6,1],(()=>w.e(75).then((()=>()=>w(7075))))),8778:()=>b("default","@lumino/widgets",[1,2,0,1]),8977:()=>b("default","@jupyterlab/fileeditor",[1,4,0,9]),9122:()=>b("default","@jupyterlab/notebook",[1,4,0,9]),9499:()=>b("default","@jupyterlab/rendermime",[1,4,0,9]),9193:()=>h("default","@jupyter-lsp/completion-theme",[1,4,0,0,,"rc",0],(()=>Promise.all([w.e(29),w.e(308),w.e(47),w.e(753)]).then((()=>()=>w(1704)))))},v={29:[6029],47:[4085,7930,9053],193:[9193],211:[6211],308:[7308],661:[143,667,828,1165,1589,2697,2964,3205,3849,4034,4901,6264,6697,6797,7138,7454,7749,7807,8190,8204,8429,8644,8778,8977,9122,9499]},w.f.consumes=(e,r)=>{w.o(v,e)&&v[e].forEach((e=>{if(w.o(m,e))return r.push(m[e]);var t=r=>{m[e]=0,w.m[e]=t=>{delete w.c[e],t.exports=r()}},a=r=>{delete m[e],w.m[e]=t=>{throw delete w.c[e],r}};try{var l=y[e]();l.then?r.push(m[e]=l.then(t).catch(a)):t(l)}catch(e){a(e)}}))},(()=>{var e={80:0};w.f.j=(r,t)=>{var a=w.o(e,r)?e[r]:void 0;if(0!==a)if(a)t.push(a[2]);else if(/^(193|211|29|308|47)$/.test(r))e[r]=0;else{var l=new Promise(((t,l)=>a=e[r]=[t,l]));t.push(a[2]=l);var o=w.p+w.u(r),n=new Error;w.l(o,(t=>{if(w.o(e,r)&&(0!==(a=e[r])&&(e[r]=void 0),a)){var l=t&&("load"===t.type?"missing":t.type),o=t&&t.target&&t.target.src;n.message="Loading chunk "+r+" failed.\n("+l+": "+o+")",n.name="ChunkLoadError",n.type=l,n.request=o,a[1](n)}}),"chunk-"+r,r)}};var r=(r,t)=>{var a,l,[o,n,u]=t,i=0;if(o.some((r=>0!==e[r]))){for(a in n)w.o(n,a)&&(w.m[a]=n[a]);u&&u(w)}for(r&&r(t);i<o.length;i++)l=o[i],w.o(e,l)&&e[l]&&e[l][0](),e[l]=0},t=self.webpackChunk_jupyter_lsp_jupyterlab_lsp=self.webpackChunk_jupyter_lsp_jupyterlab_lsp||[];t.forEach(r.bind(null,0)),t.push=r.bind(null,t.push.bind(t))})(),w.nc=void 0;var P=w(3846);(_JUPYTERLAB=void 0===_JUPYTERLAB?{}:_JUPYTERLAB)["@jupyter-lsp/jupyterlab-lsp"]=P})();
//# sourceMappingURL=remoteEntry.dc20ab8cbf84e7eee54b.js.map