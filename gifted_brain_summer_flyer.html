<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gifted Brain Schools - Summer School Fun Splash</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400&family=Bubblegum+Sans:wght@400&family=Open+Sans:wght@400;600;700;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .flyer {
            width: 8.5in;
            height: 11in;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 30%, #FF6347 70%, #DC143C 100%);
            position: relative;
            font-family: 'Open Sans', sans-serif;
            overflow: hidden;
            margin: 0 auto;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(90deg, #DC143C, #FF4500);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
            border-bottom: 5px solid #FFD700;
        }
        
        .school-name {
            font-family: 'Fredoka One', cursive;
            font-size: 28px;
            font-weight: 800;
            letter-spacing: 2px;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .tagline {
            font-size: 14px;
            font-weight: 600;
            opacity: 0.9;
        }
        
        .main-title {
            text-align: center;
            padding: 30px 20px;
            background: rgba(255, 255, 255, 0.95);
            margin: 20px;
            border-radius: 20px;
            border: 4px solid #FFD700;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .program-title {
            font-family: 'Bubblegum Sans', cursive;
            font-size: 48px;
            color: #DC143C;
            margin-bottom: 10px;
            text-shadow: 3px 3px 6px rgba(255, 215, 0, 0.5);
            transform: rotate(-2deg);
        }
        
        .splash-subtitle {
            font-size: 18px;
            color: #FF4500;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .dates {
            background: #FFD700;
            color: #DC143C;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 800;
            font-size: 16px;
            display: inline-block;
            border: 3px solid #FF4500;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
            margin-top: 10px;
        }
        
        .content-box {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            border: 3px solid #FFD700;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .content-box h3 {
            color: #DC143C;
            font-size: 18px;
            font-weight: 800;
            margin-bottom: 12px;
            text-align: center;
            background: #FFD700;
            padding: 8px;
            border-radius: 10px;
            margin: -20px -20px 15px -20px;
        }
        
        .content-box ul {
            list-style: none;
            padding: 0;
        }
        
        .content-box li {
            padding: 5px 0;
            color: #333;
            font-weight: 600;
            position: relative;
            padding-left: 20px;
        }
        
        .content-box li:before {
            content: "🌊";
            position: absolute;
            left: 0;
        }
        
        .age-groups {
            background: rgba(255, 255, 255, 0.95);
            margin: 0 20px;
            padding: 20px;
            border-radius: 15px;
            border: 3px solid #FF4500;
            text-align: center;
        }
        
        .age-groups h3 {
            color: #DC143C;
            font-size: 20px;
            font-weight: 800;
            margin-bottom: 15px;
            background: #FFD700;
            padding: 10px;
            border-radius: 10px;
            margin: -20px -20px 15px -20px;
        }
        
        .age-group {
            display: inline-block;
            background: #FF4500;
            color: white;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 20px;
            font-weight: 700;
            font-size: 14px;
        }
        
        .footer {
            background: linear-gradient(90deg, #DC143C, #FF4500);
            color: white;
            padding: 20px;
            text-align: center;
            margin-top: 20px;
            border-top: 5px solid #FFD700;
        }
        
        .contact-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }
        
        .contact-box {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 10px;
            border: 2px solid #FFD700;
        }
        
        .contact-box h4 {
            color: #FFD700;
            font-size: 14px;
            font-weight: 800;
            margin-bottom: 5px;
        }
        
        .price-highlight {
            background: #FFD700;
            color: #DC143C;
            padding: 15px;
            border-radius: 15px;
            font-size: 18px;
            font-weight: 800;
            margin: 15px 0;
            border: 3px solid #FF4500;
        }
        
        .wave-decoration {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 60px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="%23FFD700"></path><path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="%23FFD700"></path><path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="%23FFD700"></path></svg>') repeat-x;
            background-size: 1200px 120px;
        }
        
        @media print {
            .flyer {
                box-shadow: none;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="flyer">
        <div class="header">
            <div class="school-name">GIFTED BRAIN SCHOOLS</div>
            <div class="tagline">Excellence in Education • Nurturing Young Minds</div>
        </div>
        
        <div class="main-title">
            <div class="program-title">Summer School Fun Splash</div>
            <div class="splash-subtitle">🌊 Dive into Learning & Adventure! 🌊</div>
            <div class="dates">June 10th - August 2nd, 2024 • 8 Amazing Weeks!</div>
        </div>
        
        <div class="age-groups">
            <h3>🎯 Age Groups & Programs</h3>
            <div class="age-group">Little Splashers (Ages 4-6)</div>
            <div class="age-group">Wave Riders (Ages 7-9)</div>
            <div class="age-group">Ocean Explorers (Ages 10-12)</div>
            <div class="age-group">Deep Sea Scholars (Ages 13-15)</div>
        </div>
        
        <div class="content-grid">
            <div class="content-box">
                <h3>🏊‍♀️ Splash Activities</h3>
                <ul>
                    <li>Swimming & Water Safety</li>
                    <li>Beach Science Experiments</li>
                    <li>Marine Biology Discovery</li>
                    <li>Water Sports & Games</li>
                    <li>Aquatic Art Projects</li>
                    <li>Pool Party Fridays</li>
                </ul>
            </div>
            
            <div class="content-box">
                <h3>🧠 Academic Excellence</h3>
                <ul>
                    <li>Advanced Mathematics</li>
                    <li>Creative Writing Workshops</li>
                    <li>Science & Technology</li>
                    <li>Critical Thinking Skills</li>
                    <li>Language Arts</li>
                    <li>Problem Solving</li>
                </ul>
            </div>
            
            <div class="content-box">
                <h3>🎨 Creative Arts</h3>
                <ul>
                    <li>Digital Art & Design</li>
                    <li>Music & Performance</li>
                    <li>Drama & Theater</li>
                    <li>Crafts & Sculpture</li>
                    <li>Photography</li>
                    <li>Creative Expression</li>
                </ul>
            </div>
            
            <div class="content-box">
                <h3>🚀 Special Features</h3>
                <ul>
                    <li>Field Trips & Excursions</li>
                    <li>Guest Expert Speakers</li>
                    <li>Coding & Robotics</li>
                    <li>Leadership Development</li>
                    <li>Healthy Meals Included</li>
                    <li>Transportation Available</li>
                </ul>
            </div>
        </div>
        
        <div class="footer">
            <div class="price-highlight">
                💰 Early Bird Special: $275/week | Full Program: $1,950 (Save $250!)
            </div>
            
            <div class="contact-info">
                <div class="contact-box">
                    <h4>📞 Registration Hotline</h4>
                    <div>(555) 123-BRAIN</div>
                    <div>(555) 123-4567</div>
                </div>
                
                <div class="contact-box">
                    <h4>🌐 Online Registration</h4>
                    <div>www.giftedbrainschools.edu</div>
                    <div><EMAIL></div>
                </div>
            </div>
            
            <div style="font-size: 12px; margin-top: 10px;">
                📍 Multiple Locations Available | 🕐 7:30 AM - 6:00 PM | 📅 Registration Deadline: May 15th, 2024
            </div>
        </div>
        
        <div class="wave-decoration"></div>
    </div>
</body>
</html>
