var _JUPYTERLAB;(()=>{"use strict";var e,r,t,o,n,a,i,u,l,s,d,f,c,p,h,b,v,m,g,y,w={55:(e,r,t)=>{var o={"./index":()=>Promise.all([t.e(256),t.e(824)]).then((()=>()=>t(9824))),"./extension":()=>Promise.all([t.e(256),t.e(824)]).then((()=>()=>t(9824))),"./style":()=>t.e(25).then((()=>()=>t(4025)))},n=(e,r)=>(t.R=r,r=t.o(o,e)?o[e]():Promise.resolve().then((()=>{throw new Error('Module "'+e+'" does not exist in container.')})),t.R=void 0,r),a=(e,r)=>{if(t.S){var o="default",n=t.S[o];if(n&&n!==e)throw new Error("Container initialization failed as it has already been initialized with a different share scope");return t.S[o]=e,t.I(o,r)}};t.d(r,{get:()=>n,init:()=>a})}},k={};function j(e){var r=k[e];if(void 0!==r)return r.exports;var t=k[e]={id:e,loaded:!1,exports:{}};return w[e].call(t.exports,t,t.exports,j),t.loaded=!0,t.exports}j.m=w,j.c=k,j.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return j.d(r,{a:r}),r},j.d=(e,r)=>{for(var t in r)j.o(r,t)&&!j.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},j.f={},j.e=e=>Promise.all(Object.keys(j.f).reduce(((r,t)=>(j.f[t](e,r),r)),[])),j.u=e=>e+"."+{7:"2d4b977066efef2921dc",25:"a1f00326924633b92ed3",256:"3673b9e8accbe04f1c8a",805:"5070eeb32117fc99ba19",824:"85f07f87f0792b25f70b"}[e]+".js?v="+{7:"2d4b977066efef2921dc",25:"a1f00326924633b92ed3",256:"3673b9e8accbe04f1c8a",805:"5070eeb32117fc99ba19",824:"85f07f87f0792b25f70b"}[e],j.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),j.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),e={},r="@bokeh/jupyter_bokeh:",j.l=(t,o,n,a)=>{if(e[t])e[t].push(o);else{var i,u;if(void 0!==n)for(var l=document.getElementsByTagName("script"),s=0;s<l.length;s++){var d=l[s];if(d.getAttribute("src")==t||d.getAttribute("data-webpack")==r+n){i=d;break}}i||(u=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,j.nc&&i.setAttribute("nonce",j.nc),i.setAttribute("data-webpack",r+n),i.src=t),e[t]=[o];var f=(r,o)=>{i.onerror=i.onload=null,clearTimeout(c);var n=e[t];if(delete e[t],i.parentNode&&i.parentNode.removeChild(i),n&&n.forEach((e=>e(o))),r)return r(o)},c=setTimeout(f.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=f.bind(null,i.onerror),i.onload=f.bind(null,i.onload),u&&document.head.appendChild(i)}},j.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},j.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{j.S={};var e={},r={};j.I=(t,o)=>{o||(o=[]);var n=r[t];if(n||(n=r[t]={}),!(o.indexOf(n)>=0)){if(o.push(n),e[t])return e[t];j.o(j.S,t)||(j.S[t]={});var a=j.S[t],i="@bokeh/jupyter_bokeh",u=(e,r,t,o)=>{var n=a[e]=a[e]||{},u=n[r];(!u||!u.loaded&&(!o!=!u.eager?o:i>u.from))&&(n[r]={get:t,from:i,eager:!!o})},l=[];return"default"===t&&(u("@bokeh/jupyter_bokeh","4.0.5",(()=>Promise.all([j.e(256),j.e(824)]).then((()=>()=>j(9824))))),u("@jupyter-widgets/base","6.0.7",(()=>Promise.all([j.e(7),j.e(805),j.e(256)]).then((()=>()=>j(4007)))))),e[t]=l.length?Promise.all(l).then((()=>e[t]=1)):1}}})(),(()=>{var e;j.g.importScripts&&(e=j.g.location+"");var r=j.g.document;if(!e&&r&&(r.currentScript&&(e=r.currentScript.src),!e)){var t=r.getElementsByTagName("script");if(t.length)for(var o=t.length-1;o>-1&&(!e||!/^http(s?):/.test(e));)e=t[o--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),j.p=e})(),t=e=>{var r=e=>e.split(".").map((e=>+e==e?+e:e)),t=/^([^-+]+)?(?:-([^+]+))?(?:\+(.+))?$/.exec(e),o=t[1]?r(t[1]):[];return t[2]&&(o.length++,o.push.apply(o,r(t[2]))),t[3]&&(o.push([]),o.push.apply(o,r(t[3]))),o},o=(e,r)=>{e=t(e),r=t(r);for(var o=0;;){if(o>=e.length)return o<r.length&&"u"!=(typeof r[o])[0];var n=e[o],a=(typeof n)[0];if(o>=r.length)return"u"==a;var i=r[o],u=(typeof i)[0];if(a!=u)return"o"==a&&"n"==u||"s"==u||"u"==a;if("o"!=a&&"u"!=a&&n!=i)return n<i;o++}},n=e=>{var r=e[0],t="";if(1===e.length)return"*";if(r+.5){t+=0==r?">=":-1==r?"<":1==r?"^":2==r?"~":r>0?"=":"!=";for(var o=1,a=1;a<e.length;a++)o--,t+="u"==(typeof(u=e[a]))[0]?"-":(o>0?".":"")+(o=2,u);return t}var i=[];for(a=1;a<e.length;a++){var u=e[a];i.push(0===u?"not("+l()+")":1===u?"("+l()+" || "+l()+")":2===u?i.pop()+" "+i.pop():n(u))}return l();function l(){return i.pop().replace(/^\((.+)\)$/,"$1")}},a=(e,r)=>{if(0 in e){r=t(r);var o=e[0],n=o<0;n&&(o=-o-1);for(var i=0,u=1,l=!0;;u++,i++){var s,d,f=u<e.length?(typeof e[u])[0]:"";if(i>=r.length||"o"==(d=(typeof(s=r[i]))[0]))return!l||("u"==f?u>o&&!n:""==f!=n);if("u"==d){if(!l||"u"!=f)return!1}else if(l)if(f==d)if(u<=o){if(s!=e[u])return!1}else{if(n?s>e[u]:s<e[u])return!1;s!=e[u]&&(l=!1)}else if("s"!=f&&"n"!=f){if(n||u<=o)return!1;l=!1,u--}else{if(u<=o||d<f!=n)return!1;l=!1}else"s"!=f&&"n"!=f&&(l=!1,u--)}}var c=[],p=c.pop.bind(c);for(i=1;i<e.length;i++){var h=e[i];c.push(1==h?p()|p():2==h?p()&p():h?a(h,r):!p())}return!!p()},i=(e,r)=>{var t=j.S[e];if(!t||!j.o(t,r))throw new Error("Shared module "+r+" doesn't exist in shared scope "+e);return t},u=(e,r)=>{var t=e[r];return Object.keys(t).reduce(((e,r)=>!e||!t[e].loaded&&o(e,r)?r:e),0)},l=(e,r,t,o)=>"Unsatisfied version "+t+" from "+(t&&e[r][t].from)+" of shared singleton module "+r+" (required "+n(o)+")",s=(e,r,t,o)=>{var n=u(e,t);return a(o,n)||f(l(e,t,n,o)),c(e[t][n])},d=(e,r,t)=>{var n=e[r];return(r=Object.keys(n).reduce(((e,r)=>!a(t,r)||e&&!o(e,r)?e:r),0))&&n[r]},f=e=>{"undefined"!=typeof console&&console.warn&&console.warn(e)},c=e=>(e.loaded=1,e.get()),h=(p=e=>function(r,t,o,n){var a=j.I(r);return a&&a.then?a.then(e.bind(e,r,j.S[r],t,o,n)):e(r,j.S[r],t,o,n)})(((e,r,t,o)=>(i(e,t),s(r,0,t,o)))),b=p(((e,r,t,o,n)=>{var a=r&&j.o(r,t)&&d(r,t,o);return a?c(a):n()})),v={},m={5256:()=>h("default","@lumino/widgets",[1,2,3,1,,"alpha",0]),1803:()=>b("default","@jupyter-widgets/base",[,[1,6],[1,5],[1,4],[1,3],[1,2],1,1,1,1],(()=>Promise.all([j.e(7),j.e(805)]).then((()=>()=>j(4007))))),9012:()=>h("default","@lumino/disposable",[1,2,0,0]),6230:()=>h("default","@lumino/messaging",[1,2,0,0]),7262:()=>h("default","@lumino/coreutils",[1,2,0,0])},g={256:[5256],805:[6230,7262],824:[1803,9012]},y={},j.f.consumes=(e,r)=>{j.o(g,e)&&g[e].forEach((e=>{if(j.o(v,e))return r.push(v[e]);if(!y[e]){var t=r=>{v[e]=0,j.m[e]=t=>{delete j.c[e],t.exports=r()}};y[e]=!0;var o=r=>{delete v[e],j.m[e]=t=>{throw delete j.c[e],r}};try{var n=m[e]();n.then?r.push(v[e]=n.then(t).catch(o)):t(n)}catch(e){o(e)}}}))},(()=>{var e={332:0};j.f.j=(r,t)=>{var o=j.o(e,r)?e[r]:void 0;if(0!==o)if(o)t.push(o[2]);else if(/^(256|805)$/.test(r))e[r]=0;else{var n=new Promise(((t,n)=>o=e[r]=[t,n]));t.push(o[2]=n);var a=j.p+j.u(r),i=new Error;j.l(a,(t=>{if(j.o(e,r)&&(0!==(o=e[r])&&(e[r]=void 0),o)){var n=t&&("load"===t.type?"missing":t.type),a=t&&t.target&&t.target.src;i.message="Loading chunk "+r+" failed.\n("+n+": "+a+")",i.name="ChunkLoadError",i.type=n,i.request=a,o[1](i)}}),"chunk-"+r,r)}};var r=(r,t)=>{var o,n,[a,i,u]=t,l=0;if(a.some((r=>0!==e[r]))){for(o in i)j.o(i,o)&&(j.m[o]=i[o]);u&&u(j)}for(r&&r(t);l<a.length;l++)n=a[l],j.o(e,n)&&e[n]&&e[n][0](),e[n]=0},t=self.webpackChunk_bokeh_jupyter_bokeh=self.webpackChunk_bokeh_jupyter_bokeh||[];t.forEach(r.bind(null,0)),t.push=r.bind(null,t.push.bind(t))})(),j.nc=void 0;var S=j(55);(_JUPYTERLAB=void 0===_JUPYTERLAB?{}:_JUPYTERLAB)["@bokeh/jupyter_bokeh"]=S})();